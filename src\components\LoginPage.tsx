import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowRight } from "lucide-react";
import loginBg from "@/assets/login_bg.jpg";
import logo from "@/assets/logo.png";

interface LoginPageProps {
  onLogin: (role: string) => void;
}

const LoginPage = ({ onLogin }: LoginPageProps) => {
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  });

  const roles = [
    {
      value: "learner",
      label: "Learner",
      description: "Healthcare professional seeking training",
    },
    {
      value: "trainer",
      label: "Trainer",
      description: "Training program instructor",
    },
    {
      value: "manager",
      label: "Manager",
      description: "Department head or supervisor",
    },
    {
      value: "leadership",
      label: "Leadership",
      description: "Senior leadership team",
    },
    {
      value: "administrator",
      label: "Administrator",
      description: "System administrator",
    },
  ];

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedRole && credentials.email && credentials.password) {
      onLogin(selectedRole);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Background Image with Overlay Text */}
      <div className="hidden lg:flex lg:w-1/2 relative">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url(${loginBg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        />
        {/* Dark overlay for text readability */}
        <div className="absolute inset-0 bg-black/50" />
        
        {/* Content overlay */}
        <div className="relative z-10 flex flex-col justify-between h-full p-12 text-white">
          {/* Top - Brand name (centered) */}
          <div className="flex justify-center items-start pt-8">
            <h1 className="text-4xl font-bold text-center">Namaa by Aster</h1>
          </div>

          {/* Bottom - Tagline (centered) */}
          <div className="flex justify-center items-end pb-8 p-30">
            <p className="text-lg font-medium italic text-center">
             With knowledge and action, we elevate care
            </p>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md">
          {/* Logo and Welcome */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <img
                src={logo}
                alt="Medcare Logo"
                className="h-24 w-auto object-contain"
              />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Welcome
            </h2>
            <p className="text-gray-600 text-sm">
              Sign in to access your personalized dashboard
            </p>
          </div>

          {/* Login Form */}
          <Card className="bg-white shadow-lg border-0 rounded-lg">
            <CardHeader className="pb-4">
              <div className="space-y-4">
                {/* Role Selection */}
                <div className="space-y-2">
                  <Label htmlFor="role" className="text-sm font-medium text-gray-700">
                    Select Role
                  </Label>
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger id="role" className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder="Choose your role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          <div>
                            <div className="font-medium">{role.label}</div>
                            <div className="text-xs text-gray-500">
                              {role.description}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Email id */}
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email id
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email id"
                    value={credentials.email}
                    onChange={(e) =>
                      setCredentials((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    required
                  />
                </div>

                {/* Password */}
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={credentials.password}
                    onChange={(e) =>
                      setCredentials((prev) => ({
                        ...prev,
                        password: e.target.value,
                      }))
                    }
                    className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    required
                  />
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <form onSubmit={handleLogin}>
                {/* Get Started Button */}
                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200 flex items-center justify-center gap-2"
                  disabled={
                    !selectedRole ||
                    !credentials.username ||
                    !credentials.password
                  }
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Button>

                {/* Additional Links */}
                <div className="mt-6 text-center space-y-2">
                  <button 
                    type="button" 
                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Forgot password?
                  </button>
                  <div className="text-xs text-gray-500">
                    Need help? Contact IT Support
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="mt-8 text-center text-xs text-gray-500">
            <p>© 2025 Medcare LMS. All rights reserved.</p>
            <p className="mt-1">Secure • Professional • Trusted</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
