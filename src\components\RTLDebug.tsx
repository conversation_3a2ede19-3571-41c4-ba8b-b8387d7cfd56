import { useLanguage } from '@/hooks/useLanguage';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function RTLDebug() {
  const { isRTL, currentLanguage } = useLanguage();

  return (
    <Card className="w-full max-w-md mx-auto mt-4 border-2 border-red-500">
      <CardHeader>
        <CardTitle className="text-red-600">RTL Debug Info</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 text-sm">
          <p><strong>Current Language:</strong> {currentLanguage}</p>
          <p><strong>Is RTL:</strong> {isRTL ? 'Yes' : 'No'}</p>
          <p><strong>Document Dir:</strong> {document.documentElement.dir}</p>
          <p><strong>HTML Classes:</strong> {document.documentElement.className}</p>
          <p><strong>Body Classes:</strong> {document.body.className}</p>
          <div className="flex items-center gap-2 p-2 bg-gray-100 rounded">
            <div className="w-4 h-4 bg-blue-500"></div>
            <span>This should be on the {isRTL ? 'right' : 'left'}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
